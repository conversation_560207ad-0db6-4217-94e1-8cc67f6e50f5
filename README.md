# JY-MOSCOW 微信小程序

基于 JY-MONGO 后端重写的微信小程序项目。

## 项目结构

```
JY-MOSCOW/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── components/           # 自定义组件目录
│   ├── transportation/  # 货运单组件
│   ├── transportation-item/ # 货运单详情项组件
│   ├── clothing-details/ # 普通服装详情组件
│   └── oemClothing-details/ # OEM服装详情组件
├── pages/                # 页面目录
│   ├── login/           # 登录页面
│   ├── transportations/ # 货运管理页面
│   └── clothing/        # 服装管理页面
├── utils/               # 工具函数
│   ├── http.js         # HTTP 请求封装
│   └── api.js          # API 接口定义
├── images/             # 图片资源
├── test-api.js         # API 测试脚本
└── project.config.json # 项目配置文件
```

## 主要功能

### 1. 用户认证
- 微信授权登录
- JWT Token 管理
- 自动登录检测

### 2. 货运管理
- 货运单列表查看
- 按货运公司和到货状态筛选
- 更新到货日期
- 货运单详情查看

### 3. 服装管理
- 普通服装和 OEM 服装搜索
- 按年份筛选
- 服装详情查看
- 图片预览
- 价格修改（仅 OEM 服装）

## 技术特点

### 1. 新的后端集成
- 使用 JY-MONGO 后端的 JWT 认证系统
- 专门的 miniProgram 模块提供 API 服务
- 统一的错误处理和响应格式

### 2. 改进的用户体验
- 加载状态提示
- 错误处理和用户反馈
- 响应式设计
- 图片预览功能

### 3. 组件化设计
- **transportation**: 货运单卡片组件，显示货运信息和价格设置
- **transportation-item**: 货运单详情项组件，显示包装明细
- **clothing-details**: 普通服装详情组件，显示服装信息和进度
- **oemClothing-details**: OEM服装详情组件，专门显示OEM服装信息

### 4. 代码优化
- 模块化的 API 管理
- 统一的 HTTP 请求封装
- 组件化的 UI 设计
- 错误边界处理
- 代码注释和文档

## 配置说明

### 后端配置
确保 JY-MONGO 后端项目的 `.env` 文件包含以下配置：

```env
# 微信小程序配置
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d
```

### 小程序配置
在 `project.config.json` 中配置正确的 `appid`：

```json
{
  "appid": "your_wechat_appid"
}
```

### API 地址配置
在 `utils/http.js` 中配置后端 API 地址：

```javascript
const pubUrl = "http://localhost:4000/api"; // 开发环境
// const pubUrl = "https://your-production-domain.com/api"; // 生产环境
```

## 开发指南

### 1. 启动后端服务
```bash
cd JY-MONGO
pnpm install
pnpm run dev
```

服务器启动后会显示：
- 服务器运行在 http://localhost:4000
- API 文档运行在 http://localhost:4000/swagger

### 2. 导入小程序项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 JY-MOSCOW 文件夹
4. 输入正确的 AppID

### 3. API 测试
项目包含了一个 `test-api.js` 文件用于测试 API 接口：

在微信开发者工具的控制台中运行：
```javascript
// 加载测试脚本（如果需要）
// 然后运行所有测试
window.apiTest.runAllTests()

// 或者单独测试某个接口
window.apiTest.testLogin()
window.apiTest.testGetCompanyList()
```

### 4. 调试说明
- 确保后端服务正常运行在 http://localhost:4000
- 检查网络请求是否正常（在微信开发者工具中需要配置服务器域名）
- 查看控制台日志排查问题
- 使用 Swagger 文档测试 API 接口：http://localhost:4000/swagger

## API 接口

### 认证相关
- `POST /api/miniprogram/openid` - 获取微信 OpenID
- `POST /api/miniprogram/login` - 用户登录
- `POST /api/miniprogram/operate` - 添加新用户

### 货运相关
- `GET /api/miniprogram/transportationList` - 获取货运单列表
- `GET /api/miniprogram/transportationCompanyList` - 获取货运公司列表
- `POST /api/miniprogram/updateArrivedDate` - 更新到货日期

### 服装相关
- `GET /api/miniprogram/clothingInfo` - 获取普通服装信息
- `GET /api/miniprogram/OemClothingInfo` - 获取 OEM 服装信息
- `GET /api/miniprogram/searchClothing` - 搜索普通服装
- `GET /api/miniprogram/searchOemClothing` - 搜索 OEM 服装
- `POST /api/miniprogram/changePrice` - 修改价格
- `GET /api/miniprogram/clothingImgList` - 获取服装图片列表
- `GET /api/miniprogram/oemClothingImgList` - 获取 OEM 服装图片列表

## 注意事项

1. **网络配置**：确保在微信开发者工具中配置了正确的服务器域名
2. **权限管理**：所有业务接口都需要 JWT Token 认证
3. **错误处理**：接口调用失败时会自动显示错误提示
4. **数据格式**：所有日期数据使用 YYYY-MM-DD 格式
5. **图片处理**：图片预览使用微信小程序原生 API

## 组件详细说明

### 自定义组件使用指南

#### 1. transportation 组件
- **路径**: `/components/transportation/index`
- **功能**: 显示货运单卡片信息
- **属性**:
  - `transportation`: 货运单数据对象
- **事件**:
  - `myevent`: 价格弹窗关闭事件
- **特性**:
  - 显示供应商、重量、价格信息
  - 支持价格设置弹窗
  - 显示发货日期和运输天数
  - 自动计算运输天数
- **使用示例**:
```xml
<z-transportation
  transportation="{{item}}"
  bind:myevent="onGetCode"
  bindtap="onGoToDetail"
  data-item="{{item}}"
/>
```

#### 2. transportation-item 组件
- **路径**: `/components/transportation-item/index`
- **功能**: 显示货运单详情项（包装明细）
- **属性**:
  - `detail`: 包装详情数据对象
- **事件**:
  - `monitor`: 服装详情监听事件
  - `edit`: 编辑事件
- **特性**:
  - 显示包装明细和服装列表
  - 支持长按和短按事件
  - 有图片的服装显示绿色标识
  - 显示包装数量和件数
- **使用示例**:
```xml
<z-transportation-item
  detail="{{item}}"
  bind:monitor="monitor"
  bind:edit="onEdit"
/>
```

#### 3. clothing-details 组件
- **路径**: `/components/clothing-details/index`
- **功能**: 显示普通服装详情
- **属性**:
  - `clothingInfo`: 服装信息数据对象
- **特性**:
  - 显示服装基本信息和标签
  - 显示裁剪数和发货进度
  - 支持图片预览
  - 进度条动画效果
  - 分类标签颜色区分
- **使用示例**:
```xml
<z-clothing-details clothingInfo="{{item}}" />
```

#### 4. oemClothing-details 组件
- **路径**: `/components/oemClothing-details/index`
- **功能**: 显示OEM服装详情
- **属性**:
  - `oemClothingInfo`: OEM服装信息数据对象
- **特性**:
  - 显示OEM服装信息（红色边框标识）
  - 显示入库数和发货进度
  - 支持图片预览
  - 与普通服装区分的视觉设计
- **使用示例**:
```xml
<z-oemClothing-details oemClothingInfo="{{item}}" />
```

## 更新日志

### v2.0.0 (当前版本)
- ✅ 重写整个项目架构
- ✅ 集成 JY-MONGO 后端 JWT 认证
- ✅ 新增专门的 miniProgram API 模块
- ✅ 创建完整的组件化设计
- ✅ 参考原项目布局重新设计页面
- ✅ 优化用户界面和交互体验
- ✅ 改进错误处理和加载状态
- ✅ 统一代码风格和注释

### 主要改进
1. **组件化架构**: 创建了4个自定义组件，完全复制原项目的布局和交互
2. **API集成**: 13个专门的miniprogram API接口
3. **样式保持**: 保持原项目的配色方案和视觉设计
4. **功能完整**: 登录、货运管理、服装管理三大核心功能
5. **代码质量**: TypeScript类型安全，完整的错误处理

# JY-MOSCOW 服装管理小程序

## 项目概述
这是一个微信小程序项目，主要用于服装信息的管理和展示。项目采用微信小程序原生框架开发，结合Vant Weapp组件库，提供现代化的用户界面和良好的用户体验。

## 主要功能

### 1. 服装信息页面 (`pages/clothing/`)
服装信息页面是本项目的核心功能之一，提供服装信息的搜索、展示和管理功能。

#### 功能特性：
- **智能搜索**：支持按年份和服装名称搜索
- **平滑动画**：搜索后搜索栏平滑上移到顶部，结果区域淡入显示
- **双类型支持**：同时支持普通服装和OEM服装的展示
- **卡片式设计**：采用现代化的卡片布局，信息展示清晰
- **进度可视化**：通过进度条直观显示发货进度
- **图片预览**：支持服装图片的预览功能
- **价格管理**：支持在线修改服装价格

#### 技术实现：

##### 搜索组件
- 使用自定义搜索栏组件 `z-search-bar`
- 支持年份步进器选择（2020-2030年）
- 实时搜索输入和验证

##### 动画效果
- **搜索栏上移**：使用CSS3 transform和transition实现平滑上移
- **结果展示**：采用opacity和translateY实现淡入上滑效果
- **卡片动画**：每个卡片依次出现，增强视觉体验

##### 数据结构

**普通服装字段：**
```javascript
{
  clothing_name: String,        // 服装名称
  long_or_short_sleeve: String, // 长袖/短袖
  size: String,                 // 尺码
  style: String,                // 款式
  pocket_type: String,          // 口袋类型
  clipping_pcs: Number,         // 裁剪数量
  shipments: Number,            // 发货数量
  price: Number,                // 价格
  clothing_img: Array           // 服装图片
}
```

**OEM服装字段：**
```javascript
{
  oem_clothing_name: String,    // OEM服装名称
  oem_supplier: String,         // 供应商
  classification: String,       // 分类
  style: String,                // 款式
  size: String,                 // 尺码
  price: Number,                // 价格
  in_pcs: Number,               // 进货数量
  shipments: Number,            // 发货数量
  oem_clothing_img: Array       // OEM服装图片
}
```

### 2. 服装信息卡片组件 (`components/clothing-info-card/`)
专门为服装信息展示设计的可复用组件。

#### 组件特性：
- **统一接口**：通过`isOem`属性区分普通服装和OEM服装
- **响应式设计**：适配不同屏幕尺寸
- **交互反馈**：点击缩放效果，提升用户体验
- **进度可视化**：动态进度条显示发货进度
- **标签系统**：使用不同颜色标签展示服装属性

#### 使用方法：
```xml
<!-- 普通服装 -->
<clothing-info-card 
  clothingInfo="{{item}}" 
  isOem="{{false}}"
  bind:cardTap="onClothingCardTap"
  bind:changePrice="onChangePrice">
</clothing-info-card>

<!-- OEM服装 -->
<clothing-info-card 
  oemClothingInfo="{{item}}" 
  isOem="{{true}}"
  bind:cardTap="onOemClothingCardTap"
  bind:changePrice="onChangePrice">
</clothing-info-card>
```

### 3. 货运详情页面 (`pages/transportation-detail/`)
提供货运单的详细信息管理功能。

#### 主要功能：
- **到货状态管理**：支持设置和取消到货状态
- **日期选择**：集成日期选择器设置到货时间
- **数据同步**：自动计算运输天数并同步到列表页面
- **错误处理**：完善的错误处理机制，支持HTTP 201状态码

## 技术栈

### 前端技术
- **微信小程序原生框架**：使用官方框架确保最佳性能
- **Vant Weapp**：UI组件库，提供丰富的组件
- **CSS3动画**：实现流畅的过渡效果
- **ES6+**：使用现代JavaScript语法

### 开发规范
- **组件化开发**：模块化设计，提高代码复用性
- **响应式布局**：适配不同设备屏幕
- **错误处理**：完善的异常处理机制
- **性能优化**：图片懒加载、动画优化等

## 项目结构
```
JY-MOSCOW/
├── pages/                          # 页面目录
│   ├── clothing/                   # 服装管理页面
│   │   ├── clothing.js            # 页面逻辑
│   │   ├── clothing.wxml          # 页面结构
│   │   ├── clothing.wxss          # 页面样式
│   │   └── clothing.json          # 页面配置
│   ├── transportation-detail/     # 货运详情页面
│   └── transportations/           # 货运列表页面
├── components/                     # 组件目录
│   ├── clothing-info-card/        # 服装信息卡片组件
│   ├── search-bar/               # 搜索栏组件
│   └── loading-state/            # 加载状态组件
├── utils/                         # 工具类
│   └── api.js                    # API接口封装
└── README.md                     # 项目说明文档
```

## 安装和运行

### 环境要求
- 微信开发者工具
- Node.js (推荐 v14+)

### 安装步骤
1. 克隆项目到本地
2. 使用微信开发者工具打开项目
3. 安装依赖（如果使用npm包管理）
4. 配置小程序AppID
5. 编译运行

### 开发调试
- 使用微信开发者工具的调试功能
- 支持真机预览和调试
- 集成性能分析工具

## API接口

### 服装相关接口
- `Api.searchClothing(params)`：搜索普通服装
- `Api.searchOemClothing(params)`：搜索OEM服装
- `Api.changePrice(params)`：修改服装价格

### 货运相关接口
- `Api.getTransportationDetail(params)`：获取货运详情
- `Api.updateArrivedDate(params)`：更新到货时间

## 性能优化

### 动画优化
- 使用CSS3硬件加速
- 合理的动画时长和缓动函数
- 避免重排和重绘

### 数据处理
- 图片懒加载
- 数据缓存机制
- 分页加载（如需要）

### 用户体验
- 加载状态提示
- 错误状态处理
- 空状态展示

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完成服装信息页面基础功能
- ✅ 实现搜索栏平滑上移动画
- ✅ 创建服装信息卡片组件
- ✅ 支持普通服装和OEM服装展示
- ✅ 集成图片预览功能
- ✅ 实现价格修改功能
- ✅ 完善货运详情页面功能

## 后续规划
- [ ] 添加服装详情页面
- [ ] 实现数据导出功能
- [ ] 增加统计分析功能
- [ ] 优化搜索算法
- [ ] 添加离线缓存功能

## 贡献指南
欢迎提交Issue和Pull Request来改进项目。请确保：
1. 代码符合项目规范
2. 添加必要的注释
3. 测试功能正常
4. 更新相关文档

## 许可证
本项目采用MIT许可证，详见LICENSE文件。
