Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 服装信息
    clothingInfo: Object,
    // OEM服装信息
    oemClothingInfo: Object,
    // 是否为OEM服装
    isOem: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    totalPcs: 0,
    shipments: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 预览图片
     */
    showImg(e) {
     
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      console.log("info",info)
      let images = [];
      images = info.img.filter(img => img && img.url).map(img => img.url);

      
      if (images.length > 0) {
        wx.previewImage({
          urls: images,
          current: images[0]
        });
      }
    },

    /**
     * 点击卡片事件
     */
    onCardTap() {
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      this.triggerEvent('cardTap', {
        info: info,
        isOem: this.data.isOem
      });
    },

    /**
     * 修改价格
     */
    onChangePrice(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      this.triggerEvent('changePrice', {
        info: info,
        isOem: this.data.isOem
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
  },

}); 