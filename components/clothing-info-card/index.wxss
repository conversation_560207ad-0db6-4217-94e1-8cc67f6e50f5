/* 卡片容器 */
.clothing-card {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.clothing-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.card-container {
  padding: 24rpx;
}

.card-content-a {
  font-size: 50rpx;
  font-weight: bold;
}

.card-box-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.card-box-card-tag {
  display: flex;
  flex-direction: row;
  justify-content:left;
  align-items: center;
}

.card-tag{
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

/* 数量信息 */
.quantity-info {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 1rpx solid #dee2e6;
  margin-top: 10rpx;
}

.quantity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.quantity-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.quantity-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}