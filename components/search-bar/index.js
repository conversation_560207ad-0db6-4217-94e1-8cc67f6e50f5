// components/search-bar/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示年份步进器
    showYearStepper: {
      type: Boolean,
      value: false
    },
    // 年份值
    year: {
      type: Number,
      value: new Date().getFullYear()
    },

    // 搜索值
    searchValue: {
      type: String,
      value: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请输入搜索内容'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    internalSearchValue: '', // 内部搜索值
    internalYear: new Date().getFullYear() // 内部年份值
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化内部数据
      this.setData({
        internalSearchValue: this.properties.searchValue,
        internalYear: this.properties.year
      });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'searchValue': function(newVal) {
      this.setData({
        internalSearchValue: newVal
      });
    },
    'year': function(newVal) {
      this.setData({
        internalYear: newVal
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 年份改变 - 步进器事件
    onYearChange(e) {
      const newYear = e.detail;
      // 限制年份范围
      if (newYear >= this.properties.minYear && newYear <= this.properties.maxYear) {
        this.setData({
          internalYear: newYear
        });
        this.triggerEvent('yearChange', newYear);
      }
    },

    // 搜索输入变化
    onSearchInput(e) {
      const value = e.detail.value || e.detail;
      this.setData({
        internalSearchValue: value
      });
      this.triggerEvent('searchInput', value);
    },

    // 搜索确认 - 点击搜索按钮或回车
    onSearch(e) {
      this.triggerEvent('search', {
        value: this.data.internalSearchValue,
        year: this.data.internalYear
      });
    },

    // 搜索框确认事件（回车）
    onSearchConfirm(e) {
      const value = e.detail.value;
      this.setData({
        internalSearchValue: value
      });
      this.triggerEvent('search', {
        value: value,
        year: this.data.internalYear
      });
    },

    // 搜索框清空事件
    onSearchClear(e) {
      this.setData({
        internalSearchValue: ''
      });
      this.triggerEvent('searchInput', '');
    }
  }
})
