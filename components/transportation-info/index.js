// components/transportation/index.js
import Api from "../../utils/api.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    transportation: Object,
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: "",
    show: false,
    transportation_id: "",
  },

  lifetimes: {
    attached() {
      this.setData({
        price: this.properties.transportation.price || "",
      });
      // 在组件实例进入页面节点树时执行
    },
  },

  /**
   * 监听属性变化
   */
  observers: {
    'transportation.price': function(newPrice) {
      // 防止递归更新，只有当价格真的改变时才更新
      const currentPrice = this.data.price;
      const formattedNewPrice = newPrice || "";

      if (currentPrice !== formattedNewPrice) {
        this.setData({
          price: formattedNewPrice
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //显示原图
    showImg() {
      const imgList = [];
      for (
        let i = 0;
        i < this.properties.transportation.transportation_img.length;
        i++
      ) {
        imgList.push(this.properties.transportation.transportation_img[i].url);
      }
      wx.previewImage({
        urls: imgList,
      });
    },
    showPopup() {
      console.log("showPopup", this.properties.transportation);
      this.setData({
        show: true,
        transportation_id: this.properties.transportation.transportation_id,
      });
    },

    cancel() {
      this.setData({
        show: false,
        price: this.properties.transportation.price || "",
      });
    },

    async changePrice() {
      const price = this.data.price || "";
      const transportation_id = this.data.transportation_id;



      let params = {};
      params.price = price;
      params.transportation_id = transportation_id;
      console.log('paramshhh',params)

      try {
        const res = await Api.changePrice(params);
        console.log('reshhh',res)
        if (res.statusCode == 500) {
          this.setData({
            price: "",
          });
          wx.showToast({
            title: "修改失败",
            icon: "none"
          });
        } else {
          wx.showToast({
            title: "修改成功",
            icon: "success"
          });
        }
      } catch (error) {
        console.error("修改价格失败:", error);
        wx.showToast({
          title: "网络错误",
          icon: "none"
        });
      }

      this.setData({
        show: false,
      });
    },

    // 价格输入事件
    onPriceInput(e) {
      const value = e.detail.value || e.detail || "";
      this.setData({
        price: value
      });
    },

    // 价格清除事件
    onPriceClear() {
      this.setData({
        price: ""
      });
    },

    clickOverlay() {
      var myEventDetail = {
        val: false,
      }; // detail对象，提供给事件监听函数
      this.triggerEvent("myevent", myEventDetail); //myevent自定义名称事件，父组件中使用
    },
  },
});
