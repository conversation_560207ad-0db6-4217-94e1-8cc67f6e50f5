/* components/transportation/index.wxss */
.card {
  width: 675rpx;
  height: 220rpx;

  /* border: 2px solid #32682500; */
  border-radius: 5px;
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  /* transition: all 0.2s ease-in-out; */
  margin: 6px;
  position: relative;
}

.transportation {
  position: relative;
  margin-left: 30rpx;
  margin-right: 30rpx;

  /* border-bottom: 1rpx solid #EFEFEF; */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /* height: 180rpx; */
}

.detail {
  width: 100%;
  height: 140rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.a {
  display: flex;
  flex-direction: column;
  justify-content: space-between;


}

.b {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

}

.supplier {
  margin-top: 10rpx;
  margin-bottom: 15rpx;
  margin-left: 10rpx;
  width: 200rpx;
  font-size: 35rpx;
}

.weight {
  width: 200rpx;
}

.img {
  width: 120rpx;
}

.price {
  width: 140rpx;
}

.total_package_quantity {
  margin-top: 10rpx;
  width: 200rpx;
  font-size: 60rpx;
  text-align: right;
}

.date {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 10rpx;
  margin-bottom: 8rpx;
}

.price_button {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 400rpx;
}

.popup_price {

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}