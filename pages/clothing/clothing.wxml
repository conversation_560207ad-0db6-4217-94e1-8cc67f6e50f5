<!-- 搜索区域容器 -->
<view class="search-container {{hasSearchResults ? 'search-moved-up' : ''}}">
  <!-- 使用自定义搜索栏组件 -->
  <z-search-bar
    showYearStepper="{{true}}"
    year="{{year}}"
    searchValue="{{search}}"
    placeholder="搜索款号"

    disabled="{{loading}}"
    bind:yearChange="onYearChange"
    bind:searchInput="onSearchInput"
    bind:search="onGoToSearch"
  ></z-search-bar>
</view>

<!-- 搜索结果容器 -->
<view class="search-results {{hasSearchResults ? 'show-results' : ''}}">
  <!-- 普通服装列表 -->
  <view wx:for="{{clothingList}}" wx:key="index" class="clothing-item">
    <clothing-info-card
      clothingInfo="{{item}}"
      isOem="{{false}}"
      bind:cardTap="onClothingCardTap"
      bind:changePrice="onChangePrice">
    </clothing-info-card>
  </view>

  <!-- OEM服装列表 -->
  <view wx:for="{{oemClothingList}}" wx:key="index" class="clothing-item">
    <clothing-info-card
      oemClothingInfo="{{item}}"
      isOem="{{true}}"
      bind:cardTap="onOemClothingCardTap"
      bind:changePrice="onChangePrice">
    </clothing-info-card>
  </view>
</view>


<!-- 使用自定义加载状态组件 -->
<z-loading-state
  loading="{{loading}}"
  loadAll="{{false}}"
  loadingText="搜索中..."
></z-loading-state>
