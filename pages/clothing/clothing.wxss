/* 搜索区域 */
.search-container {
  background: rgb(255, 255, 255);
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  position: relative;
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center top;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(190, 190, 190, 0.15);
}

/* 搜索区域上移状态 */
.search-container.search-moved-up {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 16rpx 24rpx;
  border-radius: 0;
  box-shadow: 0 4rpx 20rpx rgba(209, 209, 209, 0.2);
  backdrop-filter: blur(10rpx);
}

/* 示例数据控制按钮 */
.demo-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  margin-top: 12rpx;
  padding: 8rpx 0;
  flex-wrap: wrap;
}

/* 搜索结果容器 */
.search-results {
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding-top: 0;
  margin-top: 16rpx;
}

.search-results.show-results {
  opacity: 1;
  transform: translateY(0);
  padding-top: 150rpx; /* 为固定的搜索栏留出空间 */
}

/* 服装项 */
.clothing-item {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(30rpx);
}

.clothing-item:nth-child(1) { animation-delay: 0.1s; }
.clothing-item:nth-child(2) { animation-delay: 0.2s; }
.clothing-item:nth-child(3) { animation-delay: 0.3s; }
.clothing-item:nth-child(4) { animation-delay: 0.4s; }
.clothing-item:nth-child(5) { animation-delay: 0.5s; }
.clothing-item:nth-child(n+6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态 */
.empty-container {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}
