// pages/transportation-detail/transportation-detail.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    transportationId: "",
    transportationDetail: [],
    transportationInfo: {}, // 货运单基本信息
    loading: true,
    error: false,
    errorMessage: "",
    // 日期选择器相关
    showDate: false,
    currentDate: new Date().getTime(),
    checked: false,
    // 服装详情弹窗
    showClothingInfo: false,
    clothingInfo: {},
    oemClothingInfo: {},
    oem: false,
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      return value;
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    console.log("options", options);
    if (id) {
      this.setData({
        transportationId: id,
      });
      this.getTransportationDetail(id);
    } else {
      this.setData({
        error: true,
        errorMessage: "缺少货运单ID参数",
        loading: false,
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 获取货运单详情
   */
  async getTransportationDetail(id) {
    try {
      this.setData({ loading: true, error: false });

      const res = await Api.getTransportationDetail({ id });
      if (res.data) {
        const detail = res.data.detail;
        const transportationInfo = res.data; // 获取货运单基本信息
        let hasArrivedDate = Boolean;
        if (transportationInfo.date_arrived) {
          hasArrivedDate = true;
        } else {
          hasArrivedDate = false;
        }

        this.setData({
          transportationDetail: detail,
          transportationInfo: transportationInfo, // 保存处理后的货运单基本信息
          checked: hasArrivedDate, // 根据是否有到货时间设置开关状态
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取货运详情失败:", error);
      this.setData({
        error: true,
        errorMessage: "网络错误，请稍后重试",
        loading: false,
      });
    }
  },

  /**
   * Switch 开关改变 - 到货状态
   */
  onChangeWitch(event) {
    const status = event.detail; // 修正参数获取方式
    console.log("Switch status changed:", status);
    const content = status ? "确认到货？" : "确认未到货？";

    wx.showModal({
      title: "提示",
      content: content,
      success: (res) => {
        if (res.confirm) {
          if (status) {
            // 确认到货 - 显示日期选择器
            this.setData({
              checked: status,
              showDate: true,
            });
          } else {
            // 确认未到货 - 直接调用取消到货方法
            this.changeTransportStatus({
              id: this.data.transportationId,
              arrived_date: null,
            });
            this.setData({
              checked: status,
            });
          }
        } else {
          // 用户取消操作，恢复开关状态
          this.setData({
            checked: !!this.data.transportationInfo.date_arrived,
          });
        }
      },
    });
  },

  /**
   * 时间选择
   */
  onInput(event) {
    this.setData({
      currentDate: event.detail,
    });
  },

  /**
   * 时间选择后确认
   */
  async onConfirm(event) {
    console.log("Date picker confirm event:", event);
    const selectedDate = this.formatDate(event.detail);
    console.log("Selected date:", selectedDate);

    // 关闭日期选择器并设置开关状态
    this.setData({
      showDate: false,
      checked: true,
    });

    // 调用API更新到货时间
    await this.changeTransportStatus({
      id: this.data.transportationId,
      arrived_date: selectedDate,
    });
  },

  /**
   * 时间选择取消
   */
  onCancel() {
    this.setData({
      showDate: false,
      checked: !!this.data.transportationInfo.date_arrived, // 恢复到原始状态
    });
  },

  /**
   * 关闭日期选择器
   */
  onCloseDatePopup() {
    this.setData({
      showDate: false,
      checked: !!this.data.transportationInfo.date_arrived, // 恢复到原始状态
    });
  },

  /**
   * 改变货运单状态
   */
  async changeTransportStatus(params) {
    try {
      console.log("更新货运状态:", params);

      const res = await Api.updateArrivedDate(params);
      console.log("API响应:", res);

      // 检查响应是否成功
      const isSuccess =
        res.statusCode >= 200 &&
        res.statusCode < 300 && // HTTP状态码成功
        ((res.data && res.data.code === 200) || // 业务状态码成功
          (res.data && res.data.success) ||
          (res.data && res.data.message === "更新成功") ||
          (res.data && !res.data.error));

      if (isSuccess) {
        // 更新本地货运单基本信息
        const updatedTransportationInfo = {
          ...this.data.transportationInfo,
          date_arrived: params.arrived_date,
        };

        // 如果有到货时间，计算天数
        if (params.arrived_date) {
          try {
            updatedTransportationInfo.days = this.calculateDays(
              params.arrived_date,
              updatedTransportationInfo.date_out
            );
          } catch (dayError) {
            console.error("计算天数失败:", dayError);
            updatedTransportationInfo.days = 0;
          }
        } else {
          // 如果取消到货时间，重新计算从出货到现在的天数
          try {
            updatedTransportationInfo.days = this.calculateDays(
              new Date(),
              updatedTransportationInfo.date_out
            );
          } catch (dayError) {
            console.error("计算天数失败:", dayError);
            updatedTransportationInfo.days = 0;
          }
        }

        this.setData({
          transportationInfo: updatedTransportationInfo,
        });

        // 通知货运列表页面更新数据
        try {
          this.notifyTransportationListUpdate(params.id, params.arrived_date);
        } catch (notifyError) {
          console.error("通知列表页面更新失败:", notifyError);
        }

        wx.showToast({
          title: params.arrived_date ? "设置到货成功" : "取消到货成功",
          icon: "success",
        });
      } else {
        console.error("API返回失败:", res);
        wx.showToast({
          title: "操作失败",
          icon: "none",
        });

        // 操作失败时恢复开关状态
        this.setData({
          checked: !!this.data.transportationInfo.date_arrived,
        });
      }
    } catch (error) {
      console.error("API调用异常:", error);

      // 确定错误消息
      let errorMessage = "网络错误，请稍后重试";
      if (error.errMsg) {
        if (error.errMsg.includes("timeout")) {
          errorMessage = "请求超时，请检查网络连接";
        } else if (error.errMsg.includes("fail")) {
          errorMessage = "网络连接失败，请检查网络设置";
        } else {
          errorMessage = `网络错误: ${error.errMsg}`;
        }
      } else if (error.statusCode) {
        errorMessage = `服务器错误: ${error.statusCode}`;
      }

      wx.showToast({
        title: errorMessage,
        icon: "none",
        duration: 3000,
      });

      // 操作失败时恢复开关状态
      this.setData({
        checked: !!this.data.transportationInfo.date_arrived,
      });
    }
  },

  /**
   * 通知货运列表页面更新数据
   */
  notifyTransportationListUpdate(transportationId, arrivedDate) {
    try {
      // 获取所有页面栈
      const pages = getCurrentPages();

      // 查找货运列表页面
      const transportationListPage = pages.find(
        (page) => page.route === "pages/transportations/transportations"
      );

      if (
        transportationListPage &&
        transportationListPage.updateTransportationItem
      ) {
        // 调用货运列表页面的更新方法
        transportationListPage.updateTransportationItem(
          transportationId,
          arrivedDate
        );
      }
    } catch (error) {
      console.error("通知货运列表页面更新失败:", error);
    }
  },

  /**
   * 计算天数
   */
  calculateDays(endDate, startDate) {
    if (!endDate || !startDate) return 0;
    const end = new Date(endDate);
    const start = new Date(startDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    try {
      // 处理空值、null、undefined
      if (!date || date === null || date === undefined) {
        return "";
      }

      // 处理字符串 "null"
      if (date === "null" || date === "NULL") {
        return "";
      }

      // 处理数字0或字符串"0"
      if (date === 0 || date === "0") {
        return "";
      }

      const d = new Date(date);

      // 检查日期是否有效
      if (isNaN(d.getTime())) {
        console.error("formatDate: 无效日期", date);
        return "";
      }

      // 检查是否是1970年（通常表示时间戳为0或接近0）
      if (d.getFullYear() === 1970) {
        return "";
      }

      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("formatDate 出错:", error, "输入:", date);
      return "";
    }
  },

  /**
   * 监听服装详情事件
   */
  async monitor(e) {
    console.log("this.monitor", e.detail.currentTarget.dataset);
    const { oem, clothing_id } = e.detail.currentTarget.dataset;

    if (oem) {
      // 显示 OEM 服装详情
      const res = await Api.getOemClothingInfo({
        oem_clothing_id: clothing_id,
      });
      console.log("res111", res);

      this.setData({
        showClothingInfo: true,
        oemClothingInfo: res.data,
        clothingInfo: {},
        oem: true,
      });
      console.log("this.setData", this.data.oemClothingInfo);
    } else {
      // 显示普通服装详情

      const res = await Api.getClothingInfo({ clothing_id: clothing_id });
      console.log("res222", res);
      this.setData({
        showClothingInfo: true,
        clothingInfo: res.data,
        oemClothingInfo: {},
        oem: false,
      });
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      clothingInfo: {},
      oemClothingInfo: {},
    });
  },

  /**
   * 重新加载数据
   */
  onRetry() {
    if (this.data.transportationId) {
      this.getTransportationDetail(this.data.transportationId);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRetry();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
});
