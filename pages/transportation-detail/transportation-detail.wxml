<view>
  <view class="popup-a">
    <view class="popup-c">
      <view class="popup-b">到货</view>
      <van-switch class="popup-switch" checked="{{ checked }}" active-color="#07c160" bind:change="onChangeWitch" />
      <van-popup show="{{ showDate }}" position="bottom" custom-style="height: 40%;" bind:close="onCloseDatePopup">
        <van-datetime-picker type="date" value="{{ currentDate }}" bind:confirm="onConfirm" bind:cancel="onCancel" bind:input="onInput" formatter="{{ formatter }}" />
      </van-popup>
    </view>
  </view>
  <view>
    <view wx:for="{{transportationDetail}}" wx:key="index">
      <z-transportation-detail-item bind:monitor="monitor" detail="{{item}}"></z-transportation-detail-item>
    </view>
  </view>
</view>
<van-popup 
  show="{{ showClothingInfo }}" 
  round 
  position="center"
  custom-style="max-width: 90vw; max-height: 80vh; padding: 0; background: transparent;"
  bind:close="onCloseClothingInfo">
  <view class="clothing-popup-container">
    <z-clothing-info-card 
      clothingInfo="{{clothingInfo}}"
      oemClothingInfo="{{oemClothingInfo}}"
      isOem="{{oem}}"
    ></z-clothing-info-card>
  </view>
</van-popup>