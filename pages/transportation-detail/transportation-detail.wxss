.popup-a {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin: 28.8rpx 28.8rpx;
  /* border-bottom: 1rpx solid #a01c1c; */
  /* box-sizing: border-box; */
}

.popup-button {
  margin-top: 7rpx;
  margin-right: 100rpx;
}

.popup-switch {
  margin-top: 7rpx;
}

.popup-b {
  font-size: 55rpx;
  text-align: center;
  margin-right: 50rpx;
}

.popup-c {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.popup-clothing-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-more-container {
  padding-bottom: 40rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
.loading-more-text {
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 服装信息弹窗容器 */
.clothing-popup-container {
  width: 100%;
  min-width: 600rpx;
  max-width: 90vw;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-sizing: border-box;
}

/* 确保弹窗内的组件样式正常 */
.clothing-popup-container .clothing-card {
  margin: 0 !important;
  box-shadow: none !important;
  border-radius: 16rpx !important;
  background: transparent !important;
}

/* 弹窗内卡片容器的内边距调整 */
.clothing-popup-container .card-container {
  padding: 32rpx !important;
}
