<page-meta page-style="{{ show ? 'overflow: hidden;' : '' }}" />

<!-- 筛选器 -->
<view class="filter-container">
  <van-dropdown-menu active-color="#98281e">
    <van-dropdown-item 
      value="{{ selectedSupplier }}" 
      options="{{ supplierOptions }}"
      bind:change="onSupplierChange"
    />
    <van-dropdown-item 
      value="{{ selectedStatus }}" 
      options="{{ statusOptions }}"
      bind:change="onStatusChange"
    />
  </van-dropdown-menu>
</view>

<!-- 货运单列表 -->
<view class="list-container">
  <view wx:for="{{list}}" wx:key="index">
    <z-transportation-info
      bindtap='onGoToDetail'
      bind:myevent="onGetCode"
      transportation="{{item}}"
      data-item="{{item}}"
    >
    </z-transportation-info>
  </view>
</view>

<!-- 使用自定义加载状态组件 -->
<z-loading-state
  loading="{{loadMore}}"
  loadAll="{{loadAll}}"
  loadingText="加载中..."
  loadAllText="到底了"
></z-loading-state>
