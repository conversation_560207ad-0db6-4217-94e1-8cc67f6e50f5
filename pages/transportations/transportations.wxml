<page-meta page-style="{{ show ? 'overflow: hidden;' : '' }}" />

<!-- 筛选器 - 使用自定义下拉选择器 -->
<view class="filter-container-custom">
  <view class="filter-row">
    <view class="filter-item">
      <z-custom-dropdown
        value="{{ selectedSupplier }}"
        options="{{ supplierOptions }}"
        placeholder="选择供应商"
        bind:change="onSupplierChange"
      />
    </view>
    <view class="filter-item">
      <z-custom-dropdown
        value="{{ selectedStatus }}"
        options="{{ statusOptions }}"
        placeholder="选择状态"
        bind:change="onStatusChange"
      />
    </view>
  </view>
</view>

<!-- 备用筛选器 - 使用 Vant 组件 (如果需要可以切换) -->
<!--
<view class="filter-container" style="display: none;">
  <van-dropdown-menu active-color="#98281e" bind:close="onDropdownClose">
    <van-dropdown-item
      value="{{ selectedSupplier }}"
      options="{{ supplierOptions }}"
      bind:change="onSupplierChange"
      bind:open="onDropdownOpen"
      bind:close="onDropdownItemClose"
    />
    <van-dropdown-item
      value="{{ selectedStatus }}"
      options="{{ statusOptions }}"
      bind:change="onStatusChange"
      bind:open="onDropdownOpen"
      bind:close="onDropdownItemClose"
    />
  </van-dropdown-menu>
</view>
-->

<!-- 货运单列表 -->
<view class="list-container">
  <view wx:for="{{list}}" wx:key="index">
    <z-transportation-info
      bindtap='onGoToDetail'
      bind:myevent="onGetCode"
      transportation="{{item}}"
      data-item="{{item}}"
    >
    </z-transportation-info>
  </view>
</view>

<!-- 使用自定义加载状态组件 -->
<z-loading-state
  loading="{{loadMore}}"
  loadAll="{{loadAll}}"
  loadingText="加载中..."
  loadAllText="到底了"
></z-loading-state>
