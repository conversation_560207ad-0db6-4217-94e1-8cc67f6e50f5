
/* 筛选器容器 */
.filter-container {
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 自定义筛选器容器 */
.filter-container-custom {
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 24rpx;
}

.filter-row {
  display: flex;
  gap: 24rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

.dropdown-item {
  flex: 1;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.dropdown-item::after {
  content: '▼';
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #666;
}

/* 列表容器 */
.list-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120rpx);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}












