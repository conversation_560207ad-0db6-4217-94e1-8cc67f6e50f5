{"libVersion": "3.8.5", "projectname": "JY-MOSCOW", "condition": {"miniprogram": {"list": [{"name": "clothing", "pathName": "pages/clothing/clothing", "query": "", "scene": null, "launchMode": "default"}, {"name": "transportations", "pathName": "pages/transportations/transportations", "query": "", "launchMode": "default", "scene": null}, {"name": "login", "pathName": "pages/login/login", "query": "", "launchMode": "default", "scene": null}]}}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": true}}