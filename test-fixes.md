# 货运页面错误修复说明

## 修复的问题

### 1. autofillHideDropdown 错误
- **问题**: `TypeError: this.autofillHideDropdown is not a function`
- **原因**: 微信小程序环境中 Vant 组件库的兼容性问题
- **解决方案**: 
  - 修复了 `van-field` 组件的 `clearable` 属性设置
  - 将 `clearable` 改为 `clearable="{{ true }}"` 确保正确传递布尔值

### 2. showClear 字段设置错误
- **问题**: `Setting data field "showClear" to undefined is invalid`
- **原因**: Vant 组件内部尝试设置 undefined 值到 showClear 字段
- **解决方案**: 
  - 修复了 `van-field` 组件的属性设置
  - 确保所有布尔属性都正确传递

### 3. 递归更新错误
- **问题**: `[Component] recursive update detected: a data update is applying during another data update`
- **原因**: 
  - 选择器变化时直接调用 `getTransportationList()`
  - 没有防抖处理，导致快速连续的数据更新
  - 组件 observers 中的递归更新
- **解决方案**:
  - 添加了防抖函数 `debounceRequest()`
  - 在选择器变化事件中添加值变化检查
  - 添加请求锁定机制防止重复请求
  - 修复了组件 observers 中的递归更新问题

## 修改的文件

### pages/transportations/transportations.js
1. 添加了防抖函数 `debounceRequest()`
2. 修改了 `onSupplierChange()` 和 `onStatusChange()` 方法
3. 添加了请求锁定机制到 `getTransportationList()`
4. 添加了页面卸载时的清理方法 `onUnload()`
5. 修复了 `resetFilters()` 方法

### components/transportation-info/index.wxml
1. 修复了 `van-field` 组件的 `clearable` 属性设置

### components/transportation-info/index.js
1. 修复了 `observers` 中的递归更新问题

## 测试建议

1. 测试选择器下拉功能是否正常
2. 测试选择不同选项时是否还有错误
3. 测试快速切换选项时是否有递归更新错误
4. 测试价格输入框的清除功能是否正常

## 预期效果

- 不再出现 `autofillHideDropdown` 错误
- 不再出现 `showClear` 设置错误
- 不再出现递归更新错误
- 选择器功能正常工作
- 页面性能得到改善
